#!/bin/bash

# Validation script for CSGHub Helm chart optimizations
# This script checks if the optimization was applied correctly

set -e

CHART_DIR="$(dirname "$0")"
TEMP_DIR="/tmp/csghub-optimization-validation"
ERRORS=0

echo "🔍 Validating CSGHub Helm Chart Optimizations..."

# Clean up previous validation results
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

# Function to check if a file contains old annotation patterns
check_old_patterns() {
    local file="$1"
    local component="$2"
    
    # Check for old annotation patterns that should be replaced
    if grep -q "annotations: {{ .Values.annotations | toYaml | nindent 4 }}" "$file" 2>/dev/null; then
        echo "❌ $component: Found old deployment annotation pattern"
        ERRORS=$((ERRORS + 1))
    fi
    
    if grep -q "checksum/config: {{ include (print \$.Template.BasePath \"/configmap.yaml\") . | sha256sum }}" "$file" 2>/dev/null; then
        echo "❌ $component: Found old checksum pattern"
        ERRORS=$((ERRORS + 1))
    fi
    
    if grep -q "resource.dependencies/deployments:" "$file" 2>/dev/null; then
        echo "❌ $component: Found redundant resource dependencies annotation"
        ERRORS=$((ERRORS + 1))
    fi
}

# Function to check if a file contains new annotation patterns
check_new_patterns() {
    local file="$1"
    local component="$2"
    
    # Check for new annotation patterns
    if grep -q "common.annotations.deployment" "$file" 2>/dev/null; then
        echo "✅ $component: Uses new deployment annotation template"
    fi
    
    if grep -q "common.annotations.pod.checksum" "$file" 2>/dev/null; then
        echo "✅ $component: Uses new pod checksum template"
    fi
}

# Check core components
echo "📋 Checking core components..."

CORE_COMPONENTS=(
    "server"
    "portal"
    "accounting"
    "temporal"
    "casdoor"
    "moderation"
    "runner"
    "coredns"
    "watcher"
)

for component in "${CORE_COMPONENTS[@]}"; do
    deployment_file="$CHART_DIR/charts/$component/templates/deployment.yaml"
    if [[ -f "$deployment_file" ]]; then
        echo "Checking $component deployment..."
        check_old_patterns "$deployment_file" "$component"
        check_new_patterns "$deployment_file" "$component"
    fi
    
    configmap_file="$CHART_DIR/charts/$component/templates/configmap.yaml"
    if [[ -f "$configmap_file" ]]; then
        echo "Checking $component configmap..."
        check_old_patterns "$configmap_file" "$component"
    fi
done

# Check starship components
echo "📋 Checking starship components..."

STARSHIP_COMPONENTS=(
    "web"
    "frontend"
    "billing"
    "agentic"
    "worker"
    "megalinter-worker"
    "megalinter-server"
    "secscan"
)

for component in "${STARSHIP_COMPONENTS[@]}"; do
    deployment_file="$CHART_DIR/charts/starship/charts/$component/templates/deployment.yaml"
    if [[ -f "$deployment_file" ]]; then
        echo "Checking starship/$component deployment..."
        check_old_patterns "$deployment_file" "starship/$component"
        check_new_patterns "$deployment_file" "starship/$component"
    fi
    
    configmap_file="$CHART_DIR/charts/starship/charts/$component/templates/configmap.yaml"
    if [[ -f "$configmap_file" ]]; then
        echo "Checking starship/$component configmap..."
        check_old_patterns "$configmap_file" "starship/$component"
    fi
done

# Test template rendering
echo "📋 Testing template rendering..."

# Test basic rendering
helm template csghub-test "$CHART_DIR" \
  --set global.edition=ee \
  --set starship.enabled=true \
  --output-dir "$TEMP_DIR/test-render" \
  --debug > "$TEMP_DIR/render-debug.log" 2>&1

if [[ $? -eq 0 ]]; then
    echo "✅ Template rendering successful"
else
    echo "❌ Template rendering failed"
    ERRORS=$((ERRORS + 1))
    cat "$TEMP_DIR/render-debug.log"
fi

# Check if annotations template exists and is valid
if [[ -f "$CHART_DIR/templates/_annotations.tpl" ]]; then
    echo "✅ Annotations template file exists"
    
    # Check if required template functions are defined
    if grep -q "common.annotations.deployment" "$CHART_DIR/templates/_annotations.tpl"; then
        echo "✅ Deployment annotations template defined"
    else
        echo "❌ Deployment annotations template missing"
        ERRORS=$((ERRORS + 1))
    fi
    
    if grep -q "common.annotations.pod.checksum" "$CHART_DIR/templates/_annotations.tpl"; then
        echo "✅ Pod checksum annotations template defined"
    else
        echo "❌ Pod checksum annotations template missing"
        ERRORS=$((ERRORS + 1))
    fi
    
    if grep -q "common.annotations.ingress.nginx" "$CHART_DIR/templates/_annotations.tpl"; then
        echo "✅ Nginx ingress annotations template defined"
    else
        echo "❌ Nginx ingress annotations template missing"
        ERRORS=$((ERRORS + 1))
    fi
else
    echo "❌ Annotations template file missing"
    ERRORS=$((ERRORS + 1))
fi

# Check for duplicate template definitions
echo "📋 Checking for duplicate template definitions..."

if grep -q "common.annotations" "$CHART_DIR/templates/_labels.tpl" 2>/dev/null; then
    echo "❌ Found duplicate annotation templates in _labels.tpl"
    ERRORS=$((ERRORS + 1))
else
    echo "✅ No duplicate annotation templates found"
fi

# Summary
echo ""
echo "📊 Validation Summary:"
echo "====================="

if [[ $ERRORS -eq 0 ]]; then
    echo "🎉 All validations passed! The optimization was successful."
    echo ""
    echo "✅ Standardized annotation templates are in use"
    echo "✅ Redundant annotations have been removed"
    echo "✅ Template rendering works correctly"
    echo "✅ No duplicate template definitions found"
else
    echo "❌ Found $ERRORS issues that need to be addressed."
    exit 1
fi

echo ""
echo "🔧 Optimization Benefits:"
echo "- Consistent annotation patterns across all components"
echo "- Centralized annotation management"
echo "- Reduced code duplication"
echo "- Improved maintainability"
echo ""
echo "Validation results saved in: $TEMP_DIR"
