#!/bin/bash

# Validation script for CSGHub Helm chart auto-annotation optimizations
# This script checks if the auto-detection annotation templates are working correctly

set -e

CHART_DIR="$(dirname "$0")"
TEMP_DIR="/tmp/csghub-auto-annotations-validation"
ERRORS=0

echo "🔍 Validating CSGHub Auto-Annotation Optimizations..."

# Clean up previous validation results
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

# Function to check if a file contains auto-annotation patterns
check_auto_patterns() {
    local file="$1"
    local component="$2"
    
    # Check for auto-annotation patterns
    if grep -q "common.annotations.pod.auto" "$file" 2>/dev/null; then
        echo "✅ $component: Uses auto-detection pod annotation template"
    elif grep -q "common.annotations.pod.checksum" "$file" 2>/dev/null; then
        echo "⚠️  $component: Still uses manual checksum template (consider upgrading to auto)"
    else
        echo "❌ $component: No annotation template found"
        ERRORS=$((ERRORS + 1))
    fi
}

# Check core components
echo "📋 Checking core components for auto-annotation usage..."

CORE_COMPONENTS=(
    "server"
    "portal"
    "accounting"
    "temporal"
    "casdoor"
    "moderation"
    "runner"
    "coredns"
    "watcher"
)

for component in "${CORE_COMPONENTS[@]}"; do
    deployment_file="$CHART_DIR/charts/$component/templates/deployment.yaml"
    if [[ -f "$deployment_file" ]]; then
        echo "Checking $component deployment..."
        check_auto_patterns "$deployment_file" "$component"
    fi
done

# Check starship components
echo "📋 Checking starship components..."

STARSHIP_COMPONENTS=(
    "frontend"
    "billing"
    "agentic"
    "worker"
    "megalinter-worker"
)

for component in "${STARSHIP_COMPONENTS[@]}"; do
    deployment_file="$CHART_DIR/charts/starship/charts/$component/templates/deployment.yaml"
    if [[ -f "$deployment_file" ]]; then
        echo "Checking starship/$component deployment..."
        check_auto_patterns "$deployment_file" "starship/$component"
    fi
done

# Test template rendering with auto-detection
echo "📋 Testing auto-detection template rendering..."

# Test basic rendering
helm template csghub-auto-test "$CHART_DIR" \
  --set global.edition=ee \
  --set starship.enabled=true \
  --output-dir "$TEMP_DIR/test-render" \
  --debug > "$TEMP_DIR/render-debug.log" 2>&1

if [[ $? -eq 0 ]]; then
    echo "✅ Template rendering with auto-detection successful"
    
    # Check if checksums are properly generated
    echo "📋 Checking generated checksums..."
    
    # Look for checksum annotations in rendered templates
    if find "$TEMP_DIR/test-render" -name "*.yaml" -exec grep -l "checksum/config" {} \; | head -5; then
        echo "✅ ConfigMap checksums found in rendered templates"
    else
        echo "⚠️  No ConfigMap checksums found (may be expected if no configmaps exist)"
    fi
    
    if find "$TEMP_DIR/test-render" -name "*.yaml" -exec grep -l "checksum/secret" {} \; | head -5; then
        echo "✅ Secret checksums found in rendered templates"
    else
        echo "⚠️  No Secret checksums found (may be expected if no secrets exist)"
    fi
    
else
    echo "❌ Template rendering failed"
    ERRORS=$((ERRORS + 1))
    cat "$TEMP_DIR/render-debug.log"
fi

# Check if auto-annotation templates exist and are valid
echo "📋 Checking auto-annotation template definitions..."

if [[ -f "$CHART_DIR/templates/_annotations.tpl" ]]; then
    echo "✅ Annotations template file exists"
    
    # Check if required template functions are defined
    if grep -q "common.annotations.pod.auto" "$CHART_DIR/templates/_annotations.tpl"; then
        echo "✅ Auto-detection pod annotations template defined"
    else
        echo "❌ Auto-detection pod annotations template missing"
        ERRORS=$((ERRORS + 1))
    fi
    
    if grep -q "common.annotations.pod.smart" "$CHART_DIR/templates/_annotations.tpl"; then
        echo "✅ Smart pod annotations template defined"
    else
        echo "❌ Smart pod annotations template missing"
        ERRORS=$((ERRORS + 1))
    fi
    
else
    echo "❌ Annotations template file missing"
    ERRORS=$((ERRORS + 1))
fi

# Test auto-detection logic
echo "📋 Testing auto-detection logic..."

# Create a test values file to verify auto-detection
cat > "$TEMP_DIR/test-values.yaml" << EOF
global:
  edition: "ee"

starship:
  enabled: true

# Test component with configmap
server:
  annotations:
    test: "auto-detection"
  podAnnotations:
    test-pod: "auto-detection"
EOF

# Test rendering with custom values
helm template csghub-auto-logic-test "$CHART_DIR" \
  -f "$TEMP_DIR/test-values.yaml" \
  --output-dir "$TEMP_DIR/logic-test" \
  --debug > "$TEMP_DIR/logic-debug.log" 2>&1

if [[ $? -eq 0 ]]; then
    echo "✅ Auto-detection logic test successful"
    
    # Check if custom annotations are preserved
    if find "$TEMP_DIR/logic-test" -name "*.yaml" -exec grep -l "test.*auto-detection" {} \; | head -1; then
        echo "✅ Custom annotations preserved in auto-detection"
    else
        echo "❌ Custom annotations not preserved"
        ERRORS=$((ERRORS + 1))
    fi
    
else
    echo "❌ Auto-detection logic test failed"
    ERRORS=$((ERRORS + 1))
    cat "$TEMP_DIR/logic-debug.log"
fi

# Summary
echo ""
echo "📊 Auto-Annotation Validation Summary:"
echo "======================================"

if [[ $ERRORS -eq 0 ]]; then
    echo "🎉 All auto-annotation validations passed!"
    echo ""
    echo "✅ Auto-detection templates are properly defined"
    echo "✅ Components are using auto-annotation templates"
    echo "✅ Template rendering works correctly"
    echo "✅ Custom annotations are preserved"
    echo ""
    echo "🚀 Benefits of Auto-Detection:"
    echo "- Automatic ConfigMap/Secret checksum detection"
    echo "- No need to manually specify which templates exist"
    echo "- Intelligent component-based detection"
    echo "- Backward compatibility with manual specification"
else
    echo "❌ Found $ERRORS issues with auto-annotation implementation."
    exit 1
fi

echo ""
echo "📖 Usage Examples:"
echo "=================="
echo ""
echo "# Automatic detection (recommended):"
echo '{{- with (include "common.annotations.pod.auto" .) }}'
echo "annotations:"
echo "  {{- . | nindent 8 }}"
echo "{{- end }}"
echo ""
echo "# Manual specification (for special cases):"
echo '{{- with (include "common.annotations.pod.auto" (dict "context" . "configmap" true "secret" false)) }}'
echo "annotations:"
echo "  {{- . | nindent 8 }}"
echo "{{- end }}"
echo ""
echo "Validation results saved in: $TEMP_DIR"
