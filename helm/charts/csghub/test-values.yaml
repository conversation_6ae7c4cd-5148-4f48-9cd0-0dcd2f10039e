global:
  edition: "ee"
  image:
    registry: "opencsg-registry.cn-beijing.cr.aliyuncs.com/opencsg_public"
    tag: "v1.8.0"
    pullPolicy: "IfNotPresent"
  ingress:
    domain: "test.example.com"
    tls:
      enabled: false

# Disable most subcharts for testing
postgresql:
  enabled: false
redis:
  enabled: false
minio:
  enabled: false
gitaly:
  enabled: false
gitlab-shell:
  enabled: false
casdoor:
  enabled: false
nats:
  enabled: false
temporal:
  enabled: false
registry:
  enabled: false
coredns:
  enabled: false
ingress-nginx:
  enabled: false
fluentd:
  enabled: false
starship:
  enabled: false
dataflow:
  enabled: false
watcher:
  enabled: false

# Enable only our core services
services:
  server:
    enabled: true
  portal:
    enabled: true
  user:
    enabled: true
  accounting:
    enabled: true
