{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Create a secret for server hub API token
*/}}
{{- if .Values.services.server.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.custom" (list . "server-token") }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" (list . "server") | nindent 4 }}
type: Opaque
data:
  {{- $token := include "server.hub.api.token" . }}
  ACCOUNTING_API_KEY: {{ $token | b64enc }}
  HUB_API_TOKEN: {{ $token | b64enc }}
{{- end }}

{{/*
Create secrets for services that need them
*/}}
{{- range $serviceName, $serviceConfig := .Values.services }}
{{- $enabled := $serviceConfig.enabled }}
{{- if eq $serviceName "moderation" }}
  {{- $enabled = $.Values.global.moderation.enabled }}
{{- end }}
{{- if and $enabled $serviceConfig.secrets }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels: {{ include "common.labels" (list $ $serviceName) | nindent 4 }}
type: Opaque
data:
  {{- range $key, $value := $serviceConfig.secrets }}
  {{ $key }}: {{ $value | b64enc }}
  {{- end }}
{{- end }}
{{- end }}
