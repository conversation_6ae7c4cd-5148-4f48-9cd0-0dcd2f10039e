{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- range $serviceName, $serviceConfig := .Values.services }}
{{- $enabled := $serviceConfig.enabled }}
{{- if eq $serviceName "moderation" }}
  {{- $enabled = $.Values.global.moderation.enabled }}
{{- end }}
{{- if and $enabled (and $serviceConfig.ingress $serviceConfig.ingress.enabled) }}
---
apiVersion: {{ include "common.capabilities.ingress.apiVersion" $ }}
kind: Ingress
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels: {{ include "common.labels" (list $ $serviceName) | nindent 4 }}
  annotations:
    {{- if eq $serviceName "portal" }}
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    {{- else if eq $serviceName "server" }}
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/server-snippet: |
      client_max_body_size 0;
    {{- end }}
    {{- with $serviceConfig.ingress.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  {{- if $.Values.global.ingress.className }}
  ingressClassName: {{ $.Values.global.ingress.className }}
  {{- end }}
  {{- if $.Values.global.ingress.tls.enabled }}
  tls:
    - hosts:
        {{- if eq $serviceName "portal" }}
        - {{ include "portal.external.domain" $ }}
        {{- else if eq $serviceName "server" }}
        - {{ include "server.external.domain" $ }}
        {{- end }}
      {{- if $.Values.global.ingress.tls.secretName }}
      secretName: {{ $.Values.global.ingress.tls.secretName }}
      {{- end }}
  {{- end }}
  rules:
    {{- if eq $serviceName "portal" }}
    - host: {{ include "portal.external.domain" $ }}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: {{ include "common.names.custom" (list $ $serviceName) }}
                port:
                  number: {{ $serviceConfig.service.port }}
    {{- else if eq $serviceName "server" }}
    - host: {{ include "server.external.domain" $ }}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: {{ include "common.names.custom" (list $ $serviceName) }}
                port:
                  number: {{ $serviceConfig.service.port }}
    {{- end }}
{{- end }}
{{- end }}
