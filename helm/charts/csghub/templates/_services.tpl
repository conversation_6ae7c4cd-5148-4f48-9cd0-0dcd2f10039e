{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Define internal domain for a service
*/}}
{{- define "service.internal.domain" -}}
{{- $context := index . 0 -}}
{{- $serviceName := index . 1 -}}
{{- printf "%s-%s" $context.Release.Name $serviceName -}}
{{- end }}

{{/*
Define internal port for a service
*/}}
{{- define "service.internal.port" -}}
{{- $context := index . 0 -}}
{{- $serviceName := index . 1 -}}
{{- $serviceConfig := index $context.Values.services $serviceName -}}
{{- $serviceConfig.service.port -}}
{{- end }}

{{/*
Define internal endpoint for a service
*/}}
{{- define "service.internal.endpoint" -}}
{{- $context := index . 0 -}}
{{- $serviceName := index . 1 -}}
{{- $domain := include "service.internal.domain" . -}}
{{- $port := include "service.internal.port" . -}}
{{- printf "http://%s:%s" $domain $port -}}
{{- end }}

{{/*
Accounting service helpers
*/}}
{{- define "accounting.internal.domain" -}}
{{- printf "%s-%s" .Release.Name "accounting" -}}
{{- end }}

{{- define "accounting.internal.port" -}}
{{- .Values.services.accounting.service.port -}}
{{- end }}

{{- define "accounting.internal.endpoint" -}}
{{- printf "http://%s:%s" (include "accounting.internal.domain" .) (include "accounting.internal.port" .) -}}
{{- end }}

{{/*
Mirror service helpers
*/}}
{{- define "mirror.internal.domain" -}}
{{- printf "%s-%s" .Release.Name "mirror" -}}
{{- end }}

{{- define "mirror.internal.port" -}}
{{- .Values.services.mirror.service.port -}}
{{- end }}

{{- define "mirror.internal.endpoint" -}}
{{- printf "http://%s:%s" (include "mirror.internal.domain" .) (include "mirror.internal.port" .) -}}
{{- end }}

{{/*
Portal service helpers
*/}}
{{- define "portal.internal.domain" -}}
{{- printf "%s-%s" .Release.Name "portal" -}}
{{- end }}

{{- define "portal.internal.port" -}}
{{- .Values.services.portal.service.port -}}
{{- end }}

{{- define "portal.internal.endpoint" -}}
{{- printf "http://%s:%s" (include "portal.internal.domain" .) (include "portal.internal.port" .) -}}
{{- end }}

{{- define "portal.external.domain" -}}
{{- include "global.domain" (list . "portal") -}}
{{- end }}

{{- define "portal.external.endpoint" -}}
{{- $domain := include "portal.external.domain" . }}
{{- if eq .Values.global.ingress.service.type "NodePort" }}
{{- if .Values.global.ingress.tls.enabled -}}
{{- printf "https://%s:%s" $domain "30443" -}}
{{- else }}
{{- printf "http://%s:%s" $domain "30080" -}}
{{- end }}
{{- else }}
{{- if .Values.global.ingress.tls.enabled -}}
{{- printf "https://%s" $domain -}}
{{- else }}
{{- printf "http://%s" $domain -}}
{{- end }}
{{- end }}
{{- end }}

{{/*
Runner service helpers
*/}}
{{- define "runner.internal.domain" -}}
{{- printf "%s-%s" .Release.Name "runner" -}}
{{- end }}

{{- define "runner.internal.port" -}}
{{- .Values.services.runner.service.port -}}
{{- end }}

{{- define "runner.internal.endpoint" -}}
{{- printf "http://%s:%s" (include "runner.internal.domain" .) (include "runner.internal.port" .) -}}
{{- end }}

{{/*
Server service helpers
*/}}
{{- define "server.internal.domain" -}}
{{- printf "%s-%s" .Release.Name "server" -}}
{{- end }}

{{- define "server.internal.port" -}}
{{- if and .Values.services .Values.services.server -}}
{{- .Values.services.server.service.port -}}
{{- else -}}
8080
{{- end -}}
{{- end }}

{{- define "server.internal.endpoint" -}}
{{- printf "http://%s:%s" (include "server.internal.domain" .) (include "server.internal.port" .) -}}
{{- end }}

{{- define "server.external.domain" -}}
{{- include "csghub.external.domain" . -}}
{{- end }}

{{- define "server.external.endpoint" -}}
{{- include "csghub.external.endpoint" . -}}
{{- end }}

{{- define "server.callback.user" -}}
{{- printf "%s/api/v1/callback/user" (include "server.external.endpoint" .) -}}
{{- end }}

{{/*
User service helpers
*/}}
{{- define "user.internal.domain" -}}
{{- printf "%s-%s" .Release.Name "user" -}}
{{- end }}

{{- define "user.internal.port" -}}
{{- .Values.services.user.service.port -}}
{{- end }}

{{- define "user.internal.endpoint" -}}
{{- printf "http://%s:%s" (include "user.internal.domain" .) (include "user.internal.port" .) -}}
{{- end }}

{{/*
Generate or retrieve server hub API token
*/}}
{{- define "server.hub.api.token" -}}
{{- $token := randAlphaNum 128 }}
{{- $secretName := include "common.names.custom" (list . "server-token") -}}
{{- $secretData := (lookup "v1" "Secret" .Release.Namespace $secretName).data }}
{{- if $secretData }}
{{- $tokenFromSecret := index $secretData "HUB_API_TOKEN" }}
{{- if $tokenFromSecret }}
{{- $token = $tokenFromSecret | b64dec }}
{{- end }}
{{- end }}
{{- $token -}}
{{- end }}
