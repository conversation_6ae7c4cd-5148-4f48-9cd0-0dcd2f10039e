{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Define internal domain for a service
*/}}
{{- define "service.internal.domain" -}}
{{- $context := index . 0 -}}
{{- $serviceName := index . 1 -}}
{{- printf "%s-%s" $context.Release.Name $serviceName -}}
{{- end }}

{{/*
Define internal port for a service
*/}}
{{- define "service.internal.port" -}}
{{- $context := index . 0 -}}
{{- $serviceName := index . 1 -}}
{{- $serviceConfig := index $context.Values.services $serviceName -}}
{{- $serviceConfig.service.port -}}
{{- end }}

{{/*
Define internal endpoint for a service
*/}}
{{- define "service.internal.endpoint" -}}
{{- $context := index . 0 -}}
{{- $serviceName := index . 1 -}}
{{- $domain := include "service.internal.domain" (list $context $serviceName) -}}
{{- $port := include "service.internal.port" . -}}
{{- printf "http://%s:%s" $domain $port -}}
{{- end }}

{{/*
Accounting service helpers
*/}}
{{- define "accounting.internal.domain" -}}
{{- include "service.internal.domain" (list . "accounting") -}}
{{- end }}

{{- define "accounting.internal.port" -}}
{{- include "service.internal.port" (list . "accounting") -}}
{{- end }}

{{- define "accounting.internal.endpoint" -}}
{{- include "service.internal.endpoint" (list . "accounting") -}}
{{- end }}

{{/*
AIGateway service helpers
*/}}
{{- define "aigateway.internal.domain" -}}
{{- include "service.internal.domain" (list . "aigateway") -}}
{{- end }}

{{- define "aigateway.internal.port" -}}
{{- include "service.internal.port" (list . "aigateway") -}}
{{- end }}

{{- define "aigateway.internal.endpoint" -}}
{{- include "service.internal.endpoint" (list . "aigateway") -}}
{{- end }}

{{/*
DataViewer service helpers
*/}}
{{- define "dataviewer.internal.domain" -}}
{{- include "service.internal.domain" (list . "dataviewer") -}}
{{- end }}

{{- define "dataviewer.internal.port" -}}
{{- include "service.internal.port" (list . "dataviewer") -}}
{{- end }}

{{- define "dataviewer.internal.endpoint" -}}
{{- include "service.internal.endpoint" (list . "dataviewer") -}}
{{- end }}

{{/*
Mirror service helpers
*/}}
{{- define "mirror.internal.domain" -}}
{{- include "service.internal.domain" (list . "mirror") -}}
{{- end }}

{{- define "mirror.internal.port" -}}
{{- include "service.internal.port" (list . "mirror") -}}
{{- end }}

{{- define "mirror.internal.endpoint" -}}
{{- include "service.internal.endpoint" (list . "mirror") -}}
{{- end }}

{{/*
Moderation service helpers
*/}}
{{- define "moderation.internal.domain" -}}
{{- include "service.internal.domain" (list . "moderation") -}}
{{- end }}

{{- define "moderation.internal.port" -}}
{{- include "service.internal.port" (list . "moderation") -}}
{{- end }}

{{- define "moderation.internal.endpoint" -}}
{{- include "service.internal.endpoint" (list . "moderation") -}}
{{- end }}

{{/*
Notification service helpers
*/}}
{{- define "notification.internal.domain" -}}
{{- include "service.internal.domain" (list . "notification") -}}
{{- end }}

{{- define "notification.internal.port" -}}
{{- include "service.internal.port" (list . "notification") -}}
{{- end }}

{{- define "notification.internal.endpoint" -}}
{{- include "service.internal.endpoint" (list . "notification") -}}
{{- end }}

{{/*
Portal service helpers
*/}}
{{- define "portal.internal.domain" -}}
{{- include "service.internal.domain" (list . "portal") -}}
{{- end }}

{{- define "portal.internal.port" -}}
{{- include "service.internal.port" (list . "portal") -}}
{{- end }}

{{- define "portal.internal.endpoint" -}}
{{- include "service.internal.endpoint" (list . "portal") -}}
{{- end }}

{{- define "portal.external.domain" -}}
{{- include "global.domain" (list . "portal") -}}
{{- end }}

{{- define "portal.external.endpoint" -}}
{{- $domain := include "portal.external.domain" . }}
{{- if eq .Values.global.ingress.service.type "NodePort" }}
{{- if .Values.global.ingress.tls.enabled -}}
{{- printf "https://%s:%s" $domain "30443" -}}
{{- else }}
{{- printf "http://%s:%s" $domain "30080" -}}
{{- end }}
{{- else }}
{{- if .Values.global.ingress.tls.enabled -}}
{{- printf "https://%s" $domain -}}
{{- else }}
{{- printf "http://%s" $domain -}}
{{- end }}
{{- end }}
{{- end }}

{{/*
Proxy service helpers
*/}}
{{- define "proxy.internal.domain" -}}
{{- include "service.internal.domain" (list . "proxy") -}}
{{- end }}

{{- define "proxy.internal.port" -}}
{{- include "service.internal.port" (list . "proxy") -}}
{{- end }}

{{- define "proxy.internal.endpoint" -}}
{{- include "service.internal.endpoint" (list . "proxy") -}}
{{- end }}

{{/*
Runner service helpers
*/}}
{{- define "runner.internal.domain" -}}
{{- include "service.internal.domain" (list . "runner") -}}
{{- end }}

{{- define "runner.internal.port" -}}
{{- include "service.internal.port" (list . "runner") -}}
{{- end }}

{{- define "runner.internal.endpoint" -}}
{{- include "service.internal.endpoint" (list . "runner") -}}
{{- end }}

{{/*
Server service helpers
*/}}
{{- define "server.internal.domain" -}}
{{- include "service.internal.domain" (list . "server") -}}
{{- end }}

{{- define "server.internal.port" -}}
{{- include "service.internal.port" (list . "server") -}}
{{- end }}

{{- define "server.internal.endpoint" -}}
{{- include "service.internal.endpoint" (list . "server") -}}
{{- end }}

{{- define "server.external.domain" -}}
{{- include "csghub.external.domain" . -}}
{{- end }}

{{- define "server.external.endpoint" -}}
{{- include "csghub.external.endpoint" . -}}
{{- end }}

{{- define "server.callback.user" -}}
{{- printf "%s/api/v1/callback/user" (include "server.external.endpoint" .) -}}
{{- end }}

{{/*
User service helpers
*/}}
{{- define "user.internal.domain" -}}
{{- include "service.internal.domain" (list . "user") -}}
{{- end }}

{{- define "user.internal.port" -}}
{{- include "service.internal.port" (list . "user") -}}
{{- end }}

{{- define "user.internal.endpoint" -}}
{{- include "service.internal.endpoint" (list . "user") -}}
{{- end }}
