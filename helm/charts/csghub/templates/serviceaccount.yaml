{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- range $serviceName, $serviceConfig := .Values.services }}
{{- $enabled := $serviceConfig.enabled }}
{{- if eq $serviceName "moderation" }}
  {{- $enabled = $.Values.global.moderation.enabled }}
{{- end }}
{{- if and $enabled $serviceConfig.serviceAccount.create }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels: {{ include "common.labels" (list $ $serviceName) | nindent 4 }}
  {{- with $serviceConfig.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: {{ $serviceConfig.serviceAccount.automount }}
{{- end }}
{{- end }}
