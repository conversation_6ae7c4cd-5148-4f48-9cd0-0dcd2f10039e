{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Define the endpoint for csghub objectStore
*/}}
{{- define "csghub.objectStore.endpoint" -}}
{{- $endpoint := "http://minio.example.com" }}
{{- if .Values.objectStore }}
{{- $endpoint = or .Values.objectStore.endpoint $endpoint }}
{{- end }}
{{- if hasKey .Values.global "objectStore" }}
{{- if hasKey .Values.global.objectStore "external" }}
{{- if .Values.global.objectStore.external }}
{{- if hasKey .Values.global.objectStore "connection" }}
{{- if hasKey .Values.global.objectStore.connection "endpoint" }}
{{- if .Values.global.objectStore.connection.endpoint }}
{{- $endpoint = .Values.global.objectStore.connection.endpoint }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- $endpoint -}}
{{- end }}

{{/*
Define the accessKey for csghub objectStore
*/}}
{{- define "csghub.objectStore.accessKey" -}}
{{- $accessKey := "minio" }}
{{- if .Values.objectStore }}
{{- $accessKey = or .Values.objectStore.accessKey $accessKey }}
{{- end }}
{{- if hasKey .Values.global "objectStore" }}
{{- if hasKey .Values.global.objectStore "external" }}
{{- if .Values.global.objectStore.external }}
{{- if hasKey .Values.global.objectStore "connection" }}
{{- if hasKey .Values.global.objectStore.connection "accessKey" }}
{{- if .Values.global.objectStore.connection.accessKey }}
{{- $accessKey = .Values.global.objectStore.connection.accessKey }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- $accessKey -}}
{{- end }}

{{/*
Define the accessSecret for csghub objectStore
*/}}
{{- define "csghub.objectStore.accessSecret" -}}
{{- $accessSecret := "minio123" }}
{{- if .Values.objectStore }}
{{- $accessSecret = or .Values.objectStore.accessSecret $accessSecret }}
{{- end }}
{{- if hasKey .Values.global "objectStore" }}
{{- if hasKey .Values.global.objectStore "external" }}
{{- if .Values.global.objectStore.external }}
{{- if hasKey .Values.global.objectStore "connection" }}
{{- if hasKey .Values.global.objectStore.connection "accessSecret" }}
{{- if .Values.global.objectStore.connection.accessSecret }}
{{- $accessSecret = .Values.global.objectStore.connection.accessSecret }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- $accessSecret -}}
{{- end }}

{{/*
Define the bucket for csghub objectStore
*/}}
{{- define "csghub.objectStore.bucket" -}}
{{- $bucket := "csghub-server" }}
{{- if .Values.objectStore }}
{{- $bucket = or .Values.objectStore.bucket $bucket }}
{{- end }}
{{- if hasKey .Values.global "objectStore" }}
{{- if hasKey .Values.global.objectStore "external" }}
{{- if .Values.global.objectStore.external }}
{{- if hasKey .Values.global.objectStore "connection" }}
{{- if hasKey .Values.global.objectStore.connection "bucket" }}
{{- if .Values.global.objectStore.connection.bucket }}
{{- $bucket = .Values.global.objectStore.connection.bucket }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- $bucket -}}
{{- end }}

{{/*
Define the region for csghub objectStore
*/}}
{{- define "csghub.objectStore.region" -}}
{{- $region := "cn-north-1" }}
{{- if .Values.objectStore }}
{{- $region = or .Values.objectStore.region $region }}
{{- end }}
{{- if hasKey .Values.global "objectStore" }}
{{- if hasKey .Values.global.objectStore "external" }}
{{- if .Values.global.objectStore.external }}
{{- if hasKey .Values.global.objectStore "connection" }}
{{- if hasKey .Values.global.objectStore.connection "region" }}
{{- if .Values.global.objectStore.connection.region }}
{{- $region = .Values.global.objectStore.connection.region }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- $region -}}
{{- end }}

{{/*
Define the encrypt for csghub objectStore
*/}}
{{- define "csghub.objectStore.encrypt" -}}
{{- $encrypt := "false" }}
{{- if .Values.objectStore }}
{{- $encrypt = or .Values.objectStore.encrypt $encrypt }}
{{- end }}
{{- if eq (include "global.ingress.tls.enabled" .) "true" }}
{{- $encrypt = "true" }}
{{- end }}
{{- if hasKey .Values.global "objectStore" }}
{{- if hasKey .Values.global.objectStore "external" }}
{{- if .Values.global.objectStore.external }}
{{- if hasKey .Values.global.objectStore "connection" }}
{{- if hasKey .Values.global.objectStore.connection "encrypt" }}
{{- if .Values.global.objectStore.connection.encrypt }}
{{- $encrypt = .Values.global.objectStore.connection.encrypt }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- $encrypt -}}
{{- end }}

{{/*
Define the pathStyle for csghub objectStore
*/}}
{{- define "csghub.objectStore.pathStyle" -}}
{{- $pathStyle := "true" }}
{{- if .Values.objectStore }}
{{- $pathStyle = or .Values.objectStore.pathStyle $pathStyle }}
{{- end }}
{{- if hasKey .Values.global "objectStore" }}
{{- if hasKey .Values.global.objectStore "external" }}
{{- if .Values.global.objectStore.external }}
{{- if hasKey .Values.global.objectStore "connection" }}
{{- if hasKey .Values.global.objectStore.connection "pathStyle" }}
{{- if .Values.global.objectStore.connection.pathStyle }}
{{- $pathStyle = .Values.global.objectStore.connection.pathStyle }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- $pathStyle -}}
{{- end }}


