{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.services.server.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "common.names.custom" (list . "server-init") }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" (list . "server") | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  template:
    metadata:
      labels: {{ include "common.labels" (list . "server") | nindent 8 }}
    spec:
      restartPolicy: Never
      {{- with (or .Values.global.image.pullSecrets .Values.services.server.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      initContainers:
        - name: wait-for-postgresql
          image: {{ or .Values.global.image.registry .Values.services.server.image.registry }}/opencsg/psql:latest
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.services.server.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until pg_isready; do echo 'Wait for PostgreSQL to be ready'; sleep 2; done" ]
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list . "server") }}
          env:
            - name: PGHOST
              value: "$(STARHUB_DATABASE_HOST)"
            - name: PGPORT
              value: "$(STARHUB_DATABASE_PORT)"
            - name: PGDATABASE
              value: "$(STARHUB_DATABASE_NAME)"
            - name: PGUSER
              value: "$(STARHUB_DATABASE_USERNAME)"
            - name: PGPASSWORD
              value: "$(STARHUB_DATABASE_PASSWORD)"
        - name: wait-for-server
          image: {{ or .Values.global.image.registry .Values.services.server.image.registry }}/busybox:latest
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.services.server.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until nc -z {{ include "server.internal.domain" . }} {{ include "server.internal.port" . }}; do echo 'Wait for csghub-server to be ready'; sleep 2; done" ]
      containers:
        - name: server-init
          image: {{ or .Values.global.image.registry .Values.services.server.image.registry }}/opencsg/psql:latest
          imagePullPolicy: {{ or .Values.global.image.pullPolicy .Values.services.server.image.pullPolicy }}
          command:
            - /bin/sh
            - -c
            - |
              set -e
              echo "Starting database initialization..."
              
              # Execute SQL scripts in order
              for script in /scripts/*.sql; do
                if [ -f "$script" ]; then
                  echo "Executing $(basename $script)..."
                  psql -f "$script" || {
                    echo "Failed to execute $(basename $script)"
                    exit 1
                  }
                fi
              done
              
              echo "Database initialization completed successfully"
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list . "server") }}
          env:
            - name: PGHOST
              value: "$(STARHUB_DATABASE_HOST)"
            - name: PGPORT
              value: "$(STARHUB_DATABASE_PORT)"
            - name: PGDATABASE
              value: "$(STARHUB_DATABASE_NAME)"
            - name: PGUSER
              value: "$(STARHUB_DATABASE_USERNAME)"
            - name: PGPASSWORD
              value: "$(STARHUB_DATABASE_PASSWORD)"
          volumeMounts:
            - name: init-scripts
              mountPath: /scripts
      volumes:
        - name: init-scripts
          configMap:
            name: {{ include "common.names.custom" (list . "server-init") }}
{{- end }}
