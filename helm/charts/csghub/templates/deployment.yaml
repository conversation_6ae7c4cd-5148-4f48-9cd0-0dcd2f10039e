{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- range $serviceName, $serviceConfig := .Values.services }}
{{- $enabled := $serviceConfig.enabled }}
{{- if eq $serviceName "moderation" }}
  {{- $enabled = $.Values.global.moderation.enabled }}
{{- end }}
{{- if $enabled }}
---
apiVersion: {{ include "common.capabilities.deployment.apiVersion" $ }}
kind: Deployment
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels: {{ include "common.labels" (list $ $serviceName) | nindent 4 }}
  {{- with (include "common.annotations.deployment" (list $ $serviceName)) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" (list $ $serviceName) | nindent 6 }}
  replicas: {{ $serviceConfig.replicas }}
  revisionHistoryLimit: 1
  minReadySeconds: 30
  template:
    metadata:
      {{- with (include "common.annotations.pod.checksum" (dict "context" $ "configmap" true "secret" false "serviceName" $serviceName)) }}
      annotations:
        {{- . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "common.labels" (list $ $serviceName) | nindent 8 }}
    spec:
      {{- with (or $.Values.global.image.pullSecrets $serviceConfig.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with $serviceConfig.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if $serviceConfig.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" (list $ $serviceName) }}
      automountServiceAccountToken: {{ $serviceConfig.serviceAccount.automount }}
      {{- end }}
      initContainers:
        {{- if or (eq $serviceName "accounting") (eq $serviceName "user") (eq $serviceName "server") }}
        - name: wait-for-postgresql
          image: {{ or $.Values.global.image.registry $serviceConfig.image.registry }}/opencsg/psql:latest
          imagePullPolicy: {{ or $.Values.global.image.pullPolicy $serviceConfig.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until pg_isready; do echo 'Wait for PostgreSQL to be ready'; sleep 2; done" ]
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list $ "server") }}
          env:
            - name: PGHOST
              value: "$(STARHUB_DATABASE_HOST)"
            - name: PGPORT
              value: "$(STARHUB_DATABASE_PORT)"
            - name: PGDATABASE
              value: "$(STARHUB_DATABASE_NAME)"
            - name: PGUSER
              value: "$(STARHUB_DATABASE_USERNAME)"
            - name: PGPASSWORD
              value: "$(STARHUB_DATABASE_PASSWORD)"
        {{- end }}
        {{- if ne $serviceName "server" }}
        - name: wait-for-server
          image: {{ or $.Values.global.image.registry $serviceConfig.image.registry }}/busybox:latest
          imagePullPolicy: {{ or $.Values.global.image.pullPolicy $serviceConfig.image.pullPolicy }}
          command: [ "/bin/sh", "-c", "until nc -z {{ include "server.internal.domain" $ }} {{ include "server.internal.port" $ }}; do echo 'Wait for csghub-server to be ready'; sleep 2; done" ]
        {{- end }}
      containers:
        - name: {{ $serviceName }}
          image: {{ or $.Values.global.image.registry $serviceConfig.image.registry }}/{{ or $.Values.global.image.name $serviceConfig.image.repository }}:{{ include "csghub.image.tag" (dict "tag" (or $.Values.global.image.tag $serviceConfig.image.tag) "context" $) }}
          imagePullPolicy: {{ or $.Values.global.image.pullPolicy $serviceConfig.image.pullPolicy }}
          {{- if eq $serviceName "accounting" }}
          command: [ "/starhub-bin/starhub", "accounting", "launch" ]
          {{- else if eq $serviceName "aigateway" }}
          command: [ "/starhub-bin/starhub", "aigateway", "launch" ]
          {{- else if eq $serviceName "dataviewer" }}
          command: [ "/starhub-bin/starhub", "dataviewer", "launch" ]
          {{- else if eq $serviceName "mirror" }}
          command: [ "/starhub-bin/starhub", "mirror", "launch" ]
          {{- else if eq $serviceName "moderation" }}
          command: [ "/starhub-bin/starhub", "moderation", "launch" ]
          {{- else if eq $serviceName "notification" }}
          command: [ "/starhub-bin/starhub", "notification", "launch" ]
          {{- else if eq $serviceName "portal" }}
          command: [ "npm", "start" ]
          {{- else if eq $serviceName "proxy" }}
          command: [ "/starhub-bin/starhub", "proxy", "launch" ]
          {{- else if eq $serviceName "runner" }}
          command: [ "/starhub-bin/starhub", "runner", "launch" ]
          {{- else if eq $serviceName "server" }}
          command: [ "/starhub-bin/starhub", "server", "start" ]
          {{- else if eq $serviceName "user" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub user launch" ]
          {{- end }}
          ports:
            - containerPort: {{ $serviceConfig.service.port }}
              name: {{ $serviceName }}
              protocol: TCP
          envFrom:
            - configMapRef:
                name: {{ include "common.names.custom" (list $ $serviceName) }}
            {{- if ne $serviceName "portal" }}
            - configMapRef:
                name: {{ include "common.names.custom" (list $ "server") }}
            {{- end }}
            {{- if or (eq $serviceName "accounting") (eq $serviceName "user") }}
            - secretRef:
                name: {{ include "common.names.custom" (list $ "nats") }}
            {{- end }}
            {{- if eq $serviceName "user" }}
            - configMapRef:
                name: {{ include "common.names.custom" (list $ "casdoor") }}
            {{- end }}
          env:
            {{- if or (eq $serviceName "accounting") (eq $serviceName "user") }}
            - name: OPENCSG_ACCOUNTING_NATS_URL
              value: "nats://$(NATS_USERNAME):$(NATS_PASSWORD)@{{ include "nats.internal.domain" $ }}:{{ include "nats.internal.ports.api" $ }}"
            {{- end }}
            {{- with $serviceConfig.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- $serviceConfig.resources | toYaml | nindent 12 }}
          livenessProbe:
            tcpSocket:
              port: {{ $serviceConfig.service.port }}
            initialDelaySeconds: 20
            periodSeconds: 10
          {{- with $serviceConfig.podSecurityContext }}
          securityContext:
            {{- . | toYaml | nindent 12 }}
          {{- end }}
          {{- if eq $serviceName "user" }}
          volumeMounts:
            - name: jwt-token-crt
              mountPath: /starhub-bin/casdoor
          {{- end }}
      {{- if eq $serviceName "user" }}
      volumes:
        - name: jwt-token-crt
          secret:
            secretName: {{ include "common.names.custom" (list $ "casdoor") }}
            items:
              - key: token_jwt_key.pem
                path: token_jwt_key.pem
      {{- end }}
      {{- with $serviceConfig.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $serviceConfig.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $serviceConfig.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
{{- end }}
