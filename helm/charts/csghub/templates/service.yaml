{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- range $serviceName, $serviceConfig := .Values.services }}
{{- $enabled := $serviceConfig.enabled }}
{{- if eq $serviceName "moderation" }}
  {{- $enabled = $.Values.global.moderation.enabled }}
{{- end }}
{{- if $enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels: {{ include "common.labels" (list $ $serviceName) | nindent 4 }}
  {{- with $serviceConfig.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ $serviceConfig.service.type }}
  ports:
    - port: {{ $serviceConfig.service.port }}
      targetPort: {{ $serviceConfig.service.port }}
      protocol: TCP
      name: {{ $serviceName }}
  selector:
    {{- include "common.labels.selector" (list $ $serviceName) | nindent 4 }}
{{- end }}
{{- end }}
