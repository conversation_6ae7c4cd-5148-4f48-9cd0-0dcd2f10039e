{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- range $serviceName, $serviceConfig := .Values.services }}
{{- $enabled := $serviceConfig.enabled }}
{{- if eq $serviceName "moderation" }}
  {{- $enabled = $.Values.global.moderation.enabled }}
{{- end }}
{{- if $enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels: {{ include "common.labels" (list $ $serviceName) | nindent 4 }}
data:
  {{- if eq $serviceName "accounting" }}
  OPENCSG_ACCOUNTING_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  OPENCSG_ACCOUNTING_NOTIFY_NOBALANCE_SUBJECT: "accounting.notify.nobalance"
  OPENCSG_ACCOUNTING_MSG_FETCH_TIMEOUTINSEC: "5"
  OPENCSG_ACCOUNTING_CHARGING_ENABLE: "true"
  {{- else if eq $serviceName "aigateway" }}
  OPENCSG_AIGATEWAY_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- else if eq $serviceName "dataviewer" }}
  OPENCSG_DATAVIEWER_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- else if eq $serviceName "mirror" }}
  OPENCSG_MIRROR_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- else if eq $serviceName "moderation" }}
  OPENCSG_MODERATION_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- else if eq $serviceName "notification" }}
  OPENCSG_NOTIFICATION_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- else if eq $serviceName "portal" }}
  NEXT_PUBLIC_STARHUB_SERVER: "http://server.example.com"
  NEXT_PUBLIC_ENABLE_MULTI_SYNC: "true"
  NEXT_PUBLIC_ENABLE_HTTPS: "false"
  NEXT_PUBLIC_HF_ENDPOINT: "https://huggingface.co"
  NEXT_PUBLIC_MS_ENDPOINT: "https://modelscope.cn"
  {{- else if eq $serviceName "proxy" }}
  OPENCSG_PROXY_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- else if eq $serviceName "runner" }}
  OPENCSG_RUNNER_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- else if eq $serviceName "user" }}
  OPENCSG_USER_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- else if eq $serviceName "server" }}
  STARHUB_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  STARHUB_SERVER_PUBLIC_DOMAIN: "http://server.example.com"
  STARHUB_SERVER_FRONTEND_URL: "http://portal.example.com"
  {{- end }}
  
  {{- with $serviceConfig.environments }}
  {{- range $key, $value := . }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
  {{- end }}
{{- end }}
{{- end }}
