{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- range $serviceName, $serviceConfig := .Values.services }}
{{- $enabled := $serviceConfig.enabled }}
{{- if eq $serviceName "moderation" }}
  {{- $enabled = $.Values.global.moderation.enabled }}
{{- end }}
{{- if $enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels: {{ include "common.labels" (list $ $serviceName) | nindent 4 }}
  {{- if eq $serviceName "server" }}
  annotations:
    resource.dependencies/deployments: |
      {{ include "common.names.custom" (list $ "user") }}
      {{ include "common.names.custom" (list $ "accounting") }}
      {{ include "common.names.custom" (list $ "casdoor") }}
      {{ include "common.names.custom" (list $ "mirror") }}
      {{ include "common.names.custom" (list $ "portal") }}
      {{ include "common.names.custom" (list $ "proxy") }}
      {{ include "common.names.custom" (list $ "runner") }}
      {{ include "common.names.custom" (list $ "dataviewer") }}
      {{ include "common.names.custom" (list $ "moderation") }}
  {{- end }}
data:
  {{- if eq $serviceName "accounting" }}
  OPENCSG_ACCOUNTING_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  OPENCSG_ACCOUNTING_FEE_EVENT_SUBJECT: "accounting.fee.>"
  OPENCSG_ACCOUNTING_NOTIFY_NOBALANCE_SUBJECT: "accounting.notify.nobalance"
  OPENCSG_ACCOUNTING_MSG_FETCH_TIMEOUTINSEC: "5"
  OPENCSG_ACCOUNTING_CHARGING_ENABLE: "true"
  {{- end }}
  
  {{- if eq $serviceName "aigateway" }}
  OPENCSG_AIGATEWAY_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- end }}
  
  {{- if eq $serviceName "dataviewer" }}
  OPENCSG_DATAVIEWER_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- end }}
  
  {{- if eq $serviceName "mirror" }}
  OPENCSG_MIRROR_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- end }}
  
  {{- if eq $serviceName "moderation" }}
  OPENCSG_MODERATION_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- end }}
  
  {{- if eq $serviceName "notification" }}
  OPENCSG_NOTIFICATION_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- end }}
  
  {{- if eq $serviceName "portal" }}
  NEXT_PUBLIC_STARHUB_SERVER: {{ include "csghub.external.endpoint" $ }}
  NEXT_PUBLIC_ENABLE_MULTI_SYNC: "true"
  NEXT_PUBLIC_ENABLE_HTTPS: {{ include "global.ingress.tls.enabled" $ | quote }}
  NEXT_PUBLIC_HF_ENDPOINT: "https://huggingface.co"
  NEXT_PUBLIC_MS_ENDPOINT: "https://modelscope.cn"
  NEXT_PUBLIC_MULTI_SYNC_ENDPOINT: {{ include "mirror.internal.endpoint" $ }}
  NEXT_PUBLIC_INTERNAL_API_HOST: {{ include "server.internal.endpoint" $ }}
  NEXT_PUBLIC_SPACE_APP_INTERNAL_DOMAIN: {{ include "global.domain" (list $ "app") }}
  NEXT_PUBLIC_SPACE_APP_INTERNAL_DOMAIN_ALIAS: {{ include "global.domain" (list $ "internal") }}
  {{- end }}
  
  {{- if eq $serviceName "proxy" }}
  OPENCSG_PROXY_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- end }}
  
  {{- if eq $serviceName "runner" }}
  OPENCSG_RUNNER_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  {{- end }}
  
  {{- if eq $serviceName "user" }}
  OPENCSG_USER_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  OPENCSG_USER_SERVER_SIGNIN_SUCCESS_REDIRECT_URL: {{ include "server.callback.user" $ }}
  {{- end }}
  
  {{- if eq $serviceName "server" }}
  # Database Configuration
  STARHUB_DATABASE_DSN: {{ include "server.postgresql.dsn" $ | quote }}
  STARHUB_DATABASE_HOST: {{ include "csghub.postgresql.host" $ }}
  STARHUB_DATABASE_PORT: {{ include "csghub.postgresql.port" $ | quote }}
  STARHUB_DATABASE_NAME: {{ include "postgresql.database" (list $ "server") }}
  STARHUB_DATABASE_USERNAME: {{ include "csghub.postgresql.user" $ }}
  STARHUB_DATABASE_PASSWORD: {{ include "csghub.postgresql.password" $ }}
  STARHUB_DATABASE_TIMEZONE: {{ include "csghub.postgresql.timezone" $ }}

  # Redis Configuration
  STARHUB_REDIS_ENDPOINT: {{ include "csghub.redis.endpoint" $ }}

  # Object Storage Configuration
  STARHUB_S3_ENDPOINT: {{ include "csghub.objectStore.endpoint" $ }}
  STARHUB_S3_ACCESS_KEY_ID: {{ include "csghub.objectStore.accessKey" $ }}
  STARHUB_S3_ACCESS_KEY_SECRET: {{ include "csghub.objectStore.accessSecret" $ }}
  STARHUB_S3_REGION: {{ include "csghub.objectStore.region" $ }}
  STARHUB_S3_BUCKET: {{ include "csghub.objectStore.bucket" $ }}
  STARHUB_S3_ENABLE_SSL: {{ include "csghub.objectStore.encrypt" $ }}
  STARHUB_S3_PATH_STYLE: {{ include "csghub.objectStore.pathStyle" $ }}

  # Server Configuration
  STARHUB_SERVER_PORT: {{ $serviceConfig.service.port | quote }}
  STARHUB_SERVER_PUBLIC_DOMAIN: {{ include "csghub.external.endpoint" $ }}
  STARHUB_SERVER_FRONTEND_URL: {{ include "portal.external.endpoint" $ }}
  STARHUB_SERVER_INTERNAL_ROOT_DOMAIN: {{ include "global.domain" (list $ "internal") }}

  # Gitaly Configuration
  STARHUB_SERVER_GITSERVER_TYPE: "gitaly"
  STARHUB_SERVER_GITALY_SERVER_SOCKET: {{ include "csghub.gitaly.endpoint" $ }}
  STARHUB_SERVER_GITALY_TOKEN: {{ include "csghub.gitaly.token" $ }}
  STARHUB_SERVER_GITALY_STORAGE: {{ include "csghub.gitaly.storage" $ }}
  {{- if eq (include "csghub.gitaly.cluster" $) "true" }}
  STARHUB_SERVER_CHECK_FILE_SIZE_ENABLED: "false"
  {{- else }}
  STARHUB_SERVER_CHECK_FILE_SIZE_ENABLED: "true"
  {{- end }}
  {{- if eq (include "gitlab-shell.external.port" $) "22" }}
  STARHUB_SERVER_SSH_DOMAIN: {{ printf "ssh://git@%s" (include "csghub.external.domain" $)}}
  {{- else }}
  STARHUB_SERVER_SSH_DOMAIN: {{ printf "ssh://git@%s:%s" (include "csghub.external.domain" $) (include "gitlab-shell.external.port" $) }}
  {{- end }}

  # Service Endpoints
  OPENCSG_ACCOUNTING_SERVER_HOST: {{ printf "http://%s" (include "accounting.internal.domain" $) }}
  OPENCSG_ACCOUNTING_SERVER_PORT: {{ include "accounting.internal.port" $ | quote }}
  OPENCSG_USER_SERVER_HOST: {{ printf "http://%s" (include "user.internal.domain" $) }}
  OPENCSG_USER_SERVER_PORT: {{ include "user.internal.port" $ | quote }}

  # Space Application
  STARHUB_SERVER_SPACE_BUILDER_ENDPOINT: {{ include "runner.internal.endpoint" $ }}
  STARHUB_SERVER_SPACE_RUNNER_ENDPOINT: {{ include "runner.internal.endpoint" $ }}

  # Registry Configuration
  STARHUB_SERVER_DOCKER_REGISTRY_NAMESPACE: {{ include "csghub.registry.namespace" $ }}
  STARHUB_SERVER_DOCKER_REGISTRY_USERNAME: {{ include "csghub.registry.username" $ }}
  STARHUB_SERVER_DOCKER_REGISTRY_PASSWORD: {{ include "csghub.registry.password" $ }}
  STARHUB_SERVER_DOCKER_REGISTRY_SERVER: {{ include "csghub.registry.repository" $ }}

  # Casdoor Configuration
  STARHUB_SERVER_CASDOOR_CLIENT_ID: "7a97bc5168cb75ffc514"
  STARHUB_SERVER_CASDOOR_CLIENT_SECRET: "33bd85106818efd90c57fb35ffc787aabbff6f7a"
  STARHUB_SERVER_CASDOOR_ENDPOINT: {{ include "casdoor.external.endpoint" $ | quote }}
  STARHUB_SERVER_CASDOOR_ORGANIZATION_NAME: "OpenCSG"
  STARHUB_SERVER_CASDOOR_APPLICATION_NAME: "CSGHub"
  {{- end }}
  
  {{- with $serviceConfig.environments }}
  {{- range $key, $value := . }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
  {{- end }}
{{- end }}
{{- end }}

{{/*
Server initialization ConfigMap for SQL scripts
*/}}
{{- if .Values.services.server.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" (list . "server-init") }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" (list . "server") | nindent 4 }}
  annotations:
    resource.dependencies/deployments: |
      {{ include "common.names.custom" (list . "user") }}
      {{ include "common.names.custom" (list . "accounting") }}
      {{ include "common.names.custom" (list . "casdoor") }}
      {{ include "common.names.custom" (list . "mirror") }}
      {{ include "common.names.custom" (list . "portal") }}
      {{ include "common.names.custom" (list . "proxy") }}
      {{ include "common.names.custom" (list . "runner") }}
data:
  {{- $currentScope := . }}
  {{- range $path, $_ := .Files.Glob "scripts/*.sql" }}
  {{ base $path }}: |
{{ $currentScope.Files.Get $path | indent 4 }}
  {{- end }}
{{- end }}
