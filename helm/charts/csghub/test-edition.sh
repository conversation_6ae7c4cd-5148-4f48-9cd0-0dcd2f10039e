#!/bin/bash

# Test script to validate edition configuration
# This script tests the template rendering for different edition configurations

set -e

CHART_DIR="$(dirname "$0")"
TEMP_DIR="/tmp/csghub-edition-test"

echo "Testing CSGHub Edition Configuration..."

# Clean up previous test results
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

# Test 1: CE Edition
echo "Test 1: Community Edition (CE)"
helm template csghub-ce "$CHART_DIR" \
  --set global.edition=ce \
  --set starship.enabled=true \
  --output-dir "$TEMP_DIR/ce" \
  --debug > "$TEMP_DIR/ce-debug.log" 2>&1

# Check that starship components are NOT rendered in CE
if find "$TEMP_DIR/ce" -name "*starship*" | grep -q .; then
  echo "❌ FAIL: Starship components found in CE edition"
  exit 1
else
  echo "✅ PASS: No Starship components in CE edition"
fi

# Check that main components have -ce suffix
if grep -r "v1.8.0-ce" "$TEMP_DIR/ce" > /dev/null; then
  echo "✅ PASS: CE image tags found"
else
  echo "❌ FAIL: CE image tags not found"
  exit 1
fi

# Test 2: EE Edition with Starship enabled
echo "Test 2: Enterprise Edition (EE) with Starship enabled"
helm template csghub-ee "$CHART_DIR" \
  --set global.edition=ee \
  --set starship.enabled=true \
  --output-dir "$TEMP_DIR/ee" \
  --debug > "$TEMP_DIR/ee-debug.log" 2>&1

# Check that starship components ARE rendered in EE with starship.enabled=true
if find "$TEMP_DIR/ee" -name "*starship*" | grep -q .; then
  echo "✅ PASS: Starship components found in EE edition"
else
  echo "❌ FAIL: Starship components not found in EE edition"
  exit 1
fi

# Check that main components have -ee suffix
if grep -r "v1.8.0-ee" "$TEMP_DIR/ee" > /dev/null; then
  echo "✅ PASS: EE image tags found"
else
  echo "❌ FAIL: EE image tags not found"
  exit 1
fi

# Test 3: EE Edition with Starship disabled
echo "Test 3: Enterprise Edition (EE) with Starship disabled"
helm template csghub-ee-no-starship "$CHART_DIR" \
  --set global.edition=ee \
  --set starship.enabled=false \
  --output-dir "$TEMP_DIR/ee-no-starship" \
  --debug > "$TEMP_DIR/ee-no-starship-debug.log" 2>&1

# Check that starship components are NOT rendered in EE with starship.enabled=false
if find "$TEMP_DIR/ee-no-starship" -name "*starship*" | grep -q .; then
  echo "❌ FAIL: Starship components found in EE edition with starship.enabled=false"
  exit 1
else
  echo "✅ PASS: No Starship components in EE edition with starship.enabled=false"
fi

# Check that main components still have -ee suffix
if grep -r "v1.8.0-ee" "$TEMP_DIR/ee-no-starship" > /dev/null; then
  echo "✅ PASS: EE image tags found even with Starship disabled"
else
  echo "❌ FAIL: EE image tags not found"
  exit 1
fi

echo ""
echo "🎉 All tests passed!"
echo "Test results saved in: $TEMP_DIR"
echo ""
echo "Summary:"
echo "- CE edition: No Starship, -ce image tags"
echo "- EE edition with Starship: Has Starship, -ee image tags"
echo "- EE edition without Starship: No Starship, -ee image tags"
